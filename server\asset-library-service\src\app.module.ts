import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ElasticsearchModule } from '@nestjs/elasticsearch';
import { JwtModule } from '@nestjs/jwt';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { PassportModule } from '@nestjs/passport';

// 配置模块
import { DatabaseConfig } from './config/database.config';
import { RedisConfig } from './config/redis.config';
import { MinioConfig } from './config/minio.config';
import { ElasticsearchConfig } from './config/elasticsearch.config';

// 核心模块
import { AssetsModule } from './modules/assets/assets.module';
import { CategoriesModule } from './modules/categories/categories.module';
import { TagsModule } from './modules/tags/tags.module';
import { VersionsModule } from './modules/versions/versions.module';
import { SearchModule } from './modules/search/search.module';
import { UploadModule } from './modules/upload/upload.module';
import { AuthModule } from './modules/auth/auth.module';

// 公共服务
import { LoggerService } from './common/services/logger.service';
import { CacheService } from './common/services/cache.service';
import { StorageService } from './common/services/storage.service';
import { AppService } from './app.service';
import { HealthController } from './common/controllers/health.controller';

// 实体
import { Asset } from './modules/assets/entities/asset.entity';
import { Category } from './modules/categories/entities/category.entity';
import { Tag } from './modules/tags/entities/tag.entity';
import { AssetVersion } from './modules/versions/entities/asset-version.entity';
import { User } from './modules/auth/entities/user.entity';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST', 'localhost'),
        port: parseInt(configService.get('DB_PORT', '3306')),
        username: configService.get('DB_USERNAME', 'root'),
        password: configService.get('DB_PASSWORD', 'password'),
        database: configService.get('DB_DATABASE_ASSET_LIBRARY') || configService.get('DB_DATABASE', 'dl_engine_asset_library'),
        entities: [Asset, Category, Tag, AssetVersion, User],
        synchronize: configService.get('NODE_ENV') !== 'production',
        logging: configService.get('NODE_ENV') === 'development',
        charset: 'utf8mb4',
        timezone: '+08:00',
        retryAttempts: 3,
        retryDelay: 3000,
        autoLoadEntities: true,
        keepConnectionAlive: true,
        extra: {
          authPlugin: 'mysql_native_password',
          ssl: false,
          connectionLimit: parseInt(configService.get('DB_POOL_MAX', '20')),
          acquireTimeout: 30000,
          timeout: 60000,
        },
      }),
      inject: [ConfigService],
    }),
    
    // Elasticsearch模块 - 条件性加载
    ...(process.env.ELASTICSEARCH_NODE ? [
      ElasticsearchModule.registerAsync({
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const config: any = {
            node: configService.get('ELASTICSEARCH_NODE', 'http://localhost:9200'),
            maxRetries: configService.get('ELASTICSEARCH_MAX_RETRIES', 3),
            requestTimeout: configService.get('ELASTICSEARCH_REQUEST_TIMEOUT', 60000),
            pingTimeout: configService.get('ELASTICSEARCH_PING_TIMEOUT', 3000),
            sniffOnStart: configService.get('ELASTICSEARCH_SNIFF_ON_START', false),
            sniffInterval: configService.get('ELASTICSEARCH_SNIFF_INTERVAL', 300000),
            sniffOnConnectionFault: configService.get('ELASTICSEARCH_SNIFF_ON_CONNECTION_FAULT', false),
            resurrectStrategy: configService.get('ELASTICSEARCH_RESURRECT_STRATEGY', 'ping'),
            log: configService.get('ELASTICSEARCH_LOG_LEVEL', 'error'),
          };

          // 只有在用户名和密码都存在时才添加认证信息
          const username = configService.get('ELASTICSEARCH_USERNAME');
          const password = configService.get('ELASTICSEARCH_PASSWORD');

          if (username && password) {
            config.auth = {
              username,
              password,
            };
          }

          return config;
        },
        inject: [ConfigService],
      })
    ] : []),
    
    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET', 'asset-library-secret'),
        signOptions: { expiresIn: '24h' },
      }),
      inject: [ConfigService],
    }),
    
    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // 微服务客户端
    ClientsModule.registerAsync([
      {
        name: 'SERVICE_REGISTRY',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('SERVICE_REGISTRY_HOST', 'localhost'),
            port: configService.get<number>('SERVICE_REGISTRY_PORT', 3010),
          },
        }),
        inject: [ConfigService],
      },
    ]),

    // 业务模块
    AssetsModule,
    CategoriesModule,
    TagsModule,
    VersionsModule,
    SearchModule,
    UploadModule,
    AuthModule,
  ],
  controllers: [HealthController],
  providers: [
    AppService,
    LoggerService,
    CacheService,
    StorageService,
    DatabaseConfig,
    RedisConfig,
    MinioConfig,
    ElasticsearchConfig,
  ],
  exports: [
    LoggerService,
    CacheService,
    StorageService,
  ],
})
export class AppModule {}
