services:
  # MySQL数据库 - Windows优化版本
  mysql:
    image: mysql:8.0
    container_name: dl-engine-mysql-win
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_ALLOW_EMPTY_PASSWORD: 'no'
      MYSQL_DATABASE: dl_engine
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - '3306:3306'
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - dl-engine-network

    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost', '-u', 'root', '-p${MYSQL_ROOT_PASSWORD}']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis缓存 - Windows优化版本
  redis:
    image: redis:7.0-alpine
    container_name: dl-engine-redis-win
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --tcp-keepalive 300
      --timeout 0
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: dl-engine-minio-win
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
      MINIO_BROWSER_REDIRECT_URL: http://localhost:9001
      MINIO_SERVER_URL: http://localhost:9000
    ports:
      - '9000:9000'
      - '9001:9001'
    volumes:
      - minio_data:/data
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9000/minio/health/live']
      interval: 30s
      timeout: 10s
      retries: 3

  # Chroma向量数据库
  chroma:
    image: chromadb/chroma:0.4.15
    container_name: dl-engine-chroma-win
    restart: unless-stopped
    environment:
      - ANONYMIZED_TELEMETRY=false
      - IS_PERSISTENT=TRUE
      - PERSIST_DIRECTORY=/chroma/chroma
    ports:
      - '8000:8000'
    volumes:
      - chroma_data:/chroma/chroma
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8000/api/v1/heartbeat']
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 60s

  # Elasticsearch搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: dl-engine-elasticsearch-win
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - '9200:9200'
      - '9300:9300'
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9200/_cluster/health']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # 服务注册中心
  service-registry:
    build:
      context: ./server
      dockerfile: service-registry/Dockerfile
    container_name: dl-engine-service-registry-win
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_registry
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_HTTP_PORT=4010
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    ports:
      - '3010:3010'
      - '4010:4010'
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4010/api/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # API网关
  api-gateway:
    build:
      context: ./server/api-gateway
      dockerfile: Dockerfile
    container_name: dl-engine-api-gateway-win
    restart: unless-stopped
    depends_on:
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3000
      - API_GATEWAY_PORT=3000
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=1d
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ASSET_SERVICE_HOST=asset-service
      - ASSET_SERVICE_PORT=3003
      - RENDER_SERVICE_HOST=render-service
      - RENDER_SERVICE_PORT=3004
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - KNOWLEDGE_SERVICE_HOST=knowledge-service
      - KNOWLEDGE_SERVICE_PORT=8008
      - RAG_ENGINE_HOST=rag-engine
      - RAG_ENGINE_PORT=8009
      - GAME_SERVER_HOST=game-server
      - GAME_SERVER_PORT=3030
      - ASSET_LIBRARY_SERVICE_HOST=asset-library-service
      - ASSET_LIBRARY_SERVICE_PORT=8003
      - BINDING_SERVICE_HOST=binding-service
      - BINDING_SERVICE_PORT=3011
      - SCENE_GENERATION_SERVICE_HOST=scene-generation-service
      - SCENE_GENERATION_SERVICE_PORT=8005
      - SCENE_TEMPLATE_SERVICE_HOST=scene-template-service
      - SCENE_TEMPLATE_SERVICE_PORT=8004
    ports:
      - '3000:3000'
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # 用户服务
  user-service:
    build:
      context: ./server
      dockerfile: user-service/Dockerfile
    container_name: dl-engine-user-service-win
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_users
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=1d
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - USER_SERVICE_HTTP_PORT=4001
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
    ports:
      - '3001:3001'
      - '4001:4001'
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4001/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # 项目服务
  project-service:
    build:
      context: ./server
      dockerfile: project-service/Dockerfile
    container_name: dl-engine-project-service-win
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_projects
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - PROJECT_SERVICE_HTTP_PORT=4002
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
    ports:
      - '3002:3002'
      - '4002:4002'
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4002/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # 资产服务
  asset-service:
    build:
      context: ./server/asset-service
      dockerfile: Dockerfile
    container_name: dl-engine-asset-service-win
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      service-registry:
        condition: service_healthy
      minio:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE_ASSETS=dl_engine_assets
      - ASSET_SERVICE_HOST=asset-service
      - ASSET_SERVICE_PORT=3003
      - ASSET_SERVICE_HTTP_PORT=4003
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - JWT_SECRET=${JWT_SECRET}
      - MAX_FILE_SIZE=104857600
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
    ports:
      - '3003:3003'
      - '4003:4003'
    volumes:
      - asset_uploads:/app/uploads
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ['CMD', 'node', '-e', 'require("http").get("http://localhost:4003/health", (res) => process.exit(res.statusCode === 200 ? 0 : 1)).on("error", () => process.exit(1))']
      interval: 30s
      timeout: 10s
      retries: 5

  # 渲染服务
  render-service:
    build:
      context: ./server
      dockerfile: render-service/Dockerfile
    container_name: dl-engine-render-service-win
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3004
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=${DB_DATABASE_RENDER}
      - RENDER_SERVICE_HOST=render-service
      - RENDER_SERVICE_PORT=3004
      - RENDER_SERVICE_HTTP_PORT=4004
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ASSET_SERVICE_HOST=asset-service
      - ASSET_SERVICE_PORT=3003
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET_RENDERS=${MINIO_BUCKET_RENDERS}
      - MAX_FILE_SIZE=${MAX_FILE_SIZE}
      - CORS_ORIGIN=${CORS_ORIGIN}
    ports:
      - '${RENDER_SERVICE_PORT}:3004'
      - '${RENDER_SERVICE_HTTP_PORT}:4004'
    volumes:
      - render_outputs:/app/renders
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4004/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # 协作服务（主实例）
  collaboration-service-1:
    build:
      context: ./server
      dockerfile: collaboration-service/Dockerfile
    container_name: dl-engine-collaboration-service-1-win
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3005
      - INSTANCE_ID=collaboration-1
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ENABLE_COMPRESSION=true
      - COMPRESSION_LEVEL=6
      - MAX_BATCH_SIZE=50
      - MAX_BATCH_WAIT_TIME=50
    ports:
      - '3005:3005'
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3005/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # 协作服务（副本实例）
  collaboration-service-2:
    build:
      context: ./server
      dockerfile: collaboration-service/Dockerfile
    container_name: dl-engine-collaboration-service-2-win
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3006
      - INSTANCE_ID=collaboration-2
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ENABLE_COMPRESSION=true
      - COMPRESSION_LEVEL=6
      - MAX_BATCH_SIZE=50
      - MAX_BATCH_WAIT_TIME=50
    ports:
      - '3006:3006'
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3006/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # 协作服务负载均衡
  collaboration-load-balancer:
    image: nginx:alpine
    container_name: dl-engine-collaboration-lb-win
    restart: unless-stopped
    depends_on:
      collaboration-service-1:
        condition: service_healthy
      collaboration-service-2:
        condition: service_healthy
    volumes:
      - type: bind
        source: ./server/collaboration-service/nginx.conf
        target: /etc/nginx/conf.d/default.conf
        read_only: true
    ports:
      - '3007:80'
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:80/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # AI模型服务（Node.js/TypeScript）
  ai-model-service:
    build:
      context: ./server
      dockerfile: ai-model-service/Dockerfile
    container_name: dl-engine-ai-model-service-win
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3008
      - MICROSERVICE_PORT=3018
      - HOST=0.0.0.0
      - MICROSERVICE_HOST=ai-model-service
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_ai
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - CORS_ORIGIN=${CORS_ORIGIN}
      - JWT_SECRET=${JWT_SECRET}
    ports:
      - '3008:3008'
      - '3018:3018'
    volumes:
      - type: volume
        source: ai_models
        target: /app/models
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3008/api/v1/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # 知识库服务
  knowledge-service:
    build:
      context: ./server/knowledge-service
      dockerfile: Dockerfile
    container_name: dl-engine-knowledge-service-win
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      chroma:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=8008
      - KNOWLEDGE_SERVICE_HOST=knowledge-service
      - KNOWLEDGE_SERVICE_PORT=8008
      - KNOWLEDGE_SERVICE_HTTP_PORT=8008
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_knowledge
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET=knowledge-bases
      - VECTOR_DB_ENDPOINT=http://chroma:8000
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=${ELASTICSEARCH_USERNAME}
      - ELASTICSEARCH_PASSWORD=${ELASTICSEARCH_PASSWORD}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - MAX_FILE_SIZE=104857600
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
    ports:
      - '8008:8008'
    volumes:
      - knowledge_uploads:/app/uploads
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8008/api/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # RAG引擎
  rag-engine:
    build:
      context: ./server/rag-engine
      dockerfile: Dockerfile
    container_name: dl-engine-rag-engine-win
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
      chroma:
        condition: service_healthy
      knowledge-service:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=8009
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - VECTOR_DB_ENDPOINT=http://chroma:8000
      - KNOWLEDGE_SERVICE_URL=http://knowledge-service:8008
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - MAX_TOKENS=4000
      - TEMPERATURE=0.7
    ports:
      - '8009:8009'
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8009/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # 游戏服务器
  game-server:
    build:
      context: ./server/game-server
      dockerfile: Dockerfile
    container_name: dl-engine-game-server-win
    restart: unless-stopped
    depends_on:
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - GAME_SERVER_PORT=3030
      - GAME_SERVER_HOST=game-server
      - GAME_SERVER_MICROSERVICE_PORT=3033
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - CORS_ORIGIN=*
    ports:
      - '3030:3030'
      - '3033:3033'
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3030/api/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # 资源库服务
  asset-library-service:
    build:
      context: ./server/asset-library-service
      dockerfile: Dockerfile
    container_name: dl-engine-asset-library-service-win
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=8003
      - HOST=0.0.0.0
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=${DB_DATABASE_ASSET_LIBRARY}
      - DB_DATABASE_ASSET_LIBRARY=${DB_DATABASE_ASSET_LIBRARY}
      - DB_POOL_MAX=20
      - DB_POOL_MIN=5
      - DB_POOL_ACQUIRE_TIMEOUT=60000
      - DB_POOL_IDLE_TIMEOUT=30000
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=0
      - REDIS_URL=redis://redis:6379/0
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET=asset-library
      - MINIO_USE_SSL=false
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ORIGIN=*
      - ELASTICSEARCH_NODE=http://elasticsearch:9200
      - LOG_LEVEL=info
    ports:
      - '8003:8003'
    volumes:
      - asset_library_uploads:/app/uploads
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ['CMD', 'node', 'dist/health-check.js']
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 60s

  # 数字人知识库绑定服务
  binding-service:
    build:
      context: ./server/binding-service
      dockerfile: Dockerfile
    container_name: dl-engine-binding-service-win
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3011
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_binding
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - JWT_SECRET=${JWT_SECRET}
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost
    ports:
      - '3011:3011'
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ['CMD', 'node', '-e', 'require("http").get("http://localhost:3011/health", (res) => process.exit(res.statusCode === 200 ? 0 : 1)).on("error", () => process.exit(1))']
      interval: 30s
      timeout: 10s
      retries: 5

  # 场景生成服务
  scene-generation-service:
    build:
      context: ./server
      dockerfile: scene-generation-service/Dockerfile
    container_name: dl-engine-scene-generation-service-win
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      service-registry:
        condition: service_healthy
      ai-model-service:
        condition: service_healthy
      asset-library-service:
        condition: service_healthy
      scene-template-service:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=8005
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=${DB_DATABASE_SCENE_GENERATION}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET_SCENE_GENERATION=${MINIO_BUCKET_SCENE_GENERATION:-scene-generation}
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - AI_MODEL_SERVICE_URL=http://ai-model-service:3008
      - ASSET_LIBRARY_SERVICE_URL=http://asset-library-service:8003
      - SCENE_TEMPLATE_SERVICE_URL=http://scene-template-service:8004
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ORIGIN=${CORS_ORIGIN}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - CACHE_TTL=${CACHE_TTL:-3600}
    ports:
      - '${SCENE_GENERATION_SERVICE_PORT}:8005'
    volumes:
      - scene_generation_data:/app/data
      - scene_generation_logs:/app/logs
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8005/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # 场景模板服务
  scene-template-service:
    build:
      context: ./server
      dockerfile: scene-template-service/Dockerfile
    container_name: dl-engine-scene-template-service-win
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - SCENE_TEMPLATE_SERVICE_PORT=8004
      - SCENE_TEMPLATE_SERVICE_HOST=0.0.0.0
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE_SCENE_TEMPLATES=dl_engine_scene_templates
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_URL=redis://redis:6379
      - REDIS_DB=1
      - MINIO_ENDPOINT=minio
      - MINIO_PORT=9000
      - MINIO_USE_SSL=false
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET=scene-templates
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ORIGIN=${CORS_ORIGIN}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    ports:
      - '8004:8004'
    volumes:
      - scene_template_data:/app/data
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8004/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # 监控服务
  monitoring-service:
    build:
      context: ./server
      dockerfile: monitoring-service/Dockerfile
      target: production
    container_name: dl-engine-monitoring-service-win
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3012
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=dl_engine_monitoring
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - CORS_ORIGIN=*
      - ELASTICSEARCH_NODE=http://elasticsearch:9200
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - ALERT_EMAIL=${ALERT_EMAIL}
    ports:
      - '3012:3012'
    volumes:
      - monitoring_logs:/app/logs
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3012/api/v1/health']
      interval: 30s
      timeout: 10s
      retries: 5

  # 编辑器前端
  editor:
    build:
      context: .
      dockerfile: editor/Dockerfile
    container_name: dl-engine-editor-win
    restart: unless-stopped
    depends_on:
      api-gateway:
        condition: service_healthy
      collaboration-load-balancer:
        condition: service_healthy
    environment:
      - REACT_APP_API_URL=http://localhost:3000/api
      - REACT_APP_COLLABORATION_SERVER_URL=ws://localhost:3007
      - REACT_APP_MINIO_ENDPOINT=http://localhost:9000
      - REACT_APP_ENVIRONMENT=production
    ports:
      - '80:80'
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:80']
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  dl-engine-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

volumes:
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/mysql
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/redis
  minio_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/minio
  chroma_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/chroma
  elasticsearch_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/elasticsearch
  asset_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/uploads/assets
  render_outputs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/outputs/renders
  knowledge_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/uploads/knowledge
  ai_models:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/models
  asset_library_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/uploads/asset-library
  scene_generation_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/scene-generation
  scene_generation_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/logs/scene-generation
  scene_template_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/scene-templates
  monitoring_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/logs/monitoring
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/prometheus
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/grafana
